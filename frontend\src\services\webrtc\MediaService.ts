// frontend/src/services/webrtc/MediaService.ts
import { store } from '../../store';
import { updateMediaPermissions, setCallingError } from '../../store/slices/callingSlice';

export interface MediaConstraints {
  audio: boolean | MediaTrackConstraints;
  video: boolean | MediaTrackConstraints;
}

export class MediaService {
  private currentStream: MediaStream | null = null;

  constructor() {
    console.log('🎥 MediaService initialized');
  }

  /**
   * Request user media with specified constraints
   * Following exact pattern from HTML test implementation
   */
  async getUserMedia(constraints: MediaConstraints): Promise<MediaStream> {
    try {
      console.log(`🎥 Requesting user media: ${JSON.stringify(constraints)}`);

      // Check if browser supports getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      this.currentStream = stream;

      // Log track information (matching HTML test)
      const audioTracks = stream.getAudioTracks();
      const videoTracks = stream.getVideoTracks();
      console.log(`📊 Local stream has ${audioTracks.length} audio tracks and ${videoTracks.length} video tracks`);

      // Update permissions in Redux store
      store.dispatch(updateMediaPermissions({
        audio: audioTracks.length > 0,
        video: videoTracks.length > 0,
      }));

      // Log track details
      audioTracks.forEach((track, index) => {
        console.log(`🎤 Audio track ${index}: ${track.label} (enabled: ${track.enabled})`);
      });

      videoTracks.forEach((track, index) => {
        console.log(`📹 Video track ${index}: ${track.label} (enabled: ${track.enabled})`);
      });

      return stream;
    } catch (error) {
      console.error('❌ Failed to get user media:', error);
      
      // Handle specific error types
      let errorMessage = 'Failed to access media devices';
      
      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
            errorMessage = 'Media access denied. Please allow camera and microphone access.';
            break;
          case 'NotFoundError':
            errorMessage = 'No camera or microphone found.';
            break;
          case 'NotReadableError':
            errorMessage = 'Camera or microphone is already in use.';
            break;
          case 'OverconstrainedError':
            errorMessage = 'Camera or microphone constraints cannot be satisfied.';
            break;
          case 'SecurityError':
            errorMessage = 'Media access blocked due to security restrictions.';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
      }

      store.dispatch(setCallingError(errorMessage));
      throw new Error(errorMessage);
    }
  }

  /**
   * Request media permissions without getting the stream
   * Useful for checking permissions before a call
   */
  async requestMediaPermissions(constraints: MediaConstraints): Promise<{ audio: boolean; video: boolean }> {
    try {
      console.log('🔐 Requesting media permissions');
      
      const stream = await this.getUserMedia(constraints);
      const permissions = {
        audio: stream.getAudioTracks().length > 0,
        video: stream.getVideoTracks().length > 0,
      };

      // Stop the stream immediately since we only wanted to check permissions
      this.stopAllTracks(stream);
      
      console.log('🔐 Media permissions granted:', permissions);
      return permissions;
    } catch (error) {
      console.error('❌ Media permissions denied:', error);
      return { audio: false, video: false };
    }
  }

  /**
   * Request audio output (speaker) permissions
   * Chrome requires explicit permission for audio output devices
   */
  async requestAudioOutputPermission(): Promise<boolean> {
    try {
      console.log('🔊 Requesting audio output permission');
      
      // Check if selectAudioOutput is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.selectAudioOutput) {
        console.warn('⚠️ selectAudioOutput not supported in this browser');
        // For browsers that don't support selectAudioOutput, assume permission is granted
        // This is mainly for Chrome 110+ requirement
        return true;
      }

      // Request audio output device selection (this grants speaker permission)
      const audioDevice = await navigator.mediaDevices.selectAudioOutput();
      
      if (audioDevice && audioDevice.deviceId) {
        console.log('✅ Audio output permission granted:', audioDevice.label || audioDevice.deviceId);
        
        // Update permissions in Redux store
        store.dispatch(updateMediaPermissions({ audioOutput: true }));
        
        return true;
      } else {
        console.warn('⚠️ No audio output device selected');
        store.dispatch(updateMediaPermissions({ audioOutput: false }));
        return false;
      }
    } catch (error) {
      console.error('❌ Audio output permission denied:', error);
      
      let errorMessage = 'Audio output permission denied';
      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
            errorMessage = 'Audio output access denied. Please allow speaker access.';
            break;
          case 'NotFoundError':
            errorMessage = 'No audio output devices found.';
            break;
          case 'InvalidStateError':
            errorMessage = 'Audio output selection not available in current context.';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
      }
      
      store.dispatch(setCallingError(errorMessage));
      store.dispatch(updateMediaPermissions({ audioOutput: false }));
      return false;
    }
  }

  /**
   * Get local stream for specific call type
   * Following HTML test patterns
   */
  async getLocalStream(callType: 'audio' | 'video'): Promise<MediaStream> {
    const constraints: MediaConstraints = this.getMediaConstraints(callType);
    return await this.getUserMedia(constraints);
  }

  /**
   * Get display media for screen sharing
   * Following HTML test screen share implementation
   */
  async getDisplayMedia(): Promise<MediaStream> {
    try {
      console.log('🖥️ Requesting display media for screen sharing');

      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        throw new Error('Screen sharing is not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          cursor: 'always',
          displaySurface: 'monitor',
        } as MediaTrackConstraints,
        audio: true,
      });

      console.log('🖥️ Screen share stream obtained');
      
      // Log track information
      const videoTracks = stream.getVideoTracks();
      const audioTracks = stream.getAudioTracks();
      console.log(`🖥️ Screen share has ${videoTracks.length} video tracks and ${audioTracks.length} audio tracks`);

      return stream;
    } catch (error) {
      console.error('❌ Failed to get display media:', error);
      
      let errorMessage = 'Failed to start screen sharing';
      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
            errorMessage = 'Screen sharing permission denied';
            break;
          case 'NotFoundError':
            errorMessage = 'No screen available for sharing';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
      }

      store.dispatch(setCallingError(errorMessage));
      throw new Error(errorMessage);
    }
  }

  /**
   * Stop all tracks in a stream with comprehensive cleanup
   * Following HTML test cleanup pattern with enhanced logging
   */
  stopAllTracks(stream: MediaStream): void {
    if (!stream) {
      console.warn('⚠️ No stream provided to stop tracks');
      return;
    }

    console.log('🛑 Starting comprehensive media track cleanup...');

    try {
      const tracks = stream.getTracks();
      console.log(`📊 Found ${tracks.length} tracks to stop`);

      tracks.forEach((track, index) => {
        console.log(`🛑 Stopping ${track.kind} track ${index}: ${track.label} (readyState: ${track.readyState})`);

        // Check if track is already stopped
        if (track.readyState === 'ended') {
          console.log(`ℹ️ Track ${index} already ended`);
        } else {
          track.stop();
          console.log(`✅ Track ${index} stopped successfully`);
        }
      });

      // Clear current stream if it matches
      if (this.currentStream === stream) {
        console.log('🔄 Clearing current stream reference');
        this.currentStream = null;
      }

      console.log('✅ Media track cleanup completed successfully');
    } catch (error) {
      console.error('❌ Error during media track cleanup:', error);

      // Force clear current stream even if cleanup failed
      if (this.currentStream === stream) {
        this.currentStream = null;
      }
    }
  }

  /**
   * Emergency cleanup - stop all current media streams
   */
  emergencyCleanup(): void {
    console.log('🚨 Emergency media cleanup initiated');

    try {
      if (this.currentStream) {
        console.log('🚨 Emergency stopping current stream');
        this.stopAllTracks(this.currentStream);
      }

      // Additional cleanup: try to stop any remaining media tracks
      // This is a safety measure to ensure all media access is released
      try {
        // Check if there are any active media tracks in the browser
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
          console.log('🔍 Checking for any remaining active media devices...');
        }
      } catch (deviceError) {
        console.warn('⚠️ Could not check media devices:', deviceError);
      }

      // Update Redux store to clear media permissions
      store.dispatch(updateMediaPermissions({
        audio: false,
        video: false,
      }));

      // Clear the current stream reference
      this.currentStream = null;

      console.log('✅ Emergency media cleanup completed');
    } catch (error) {
      console.error('❌ Error during emergency cleanup:', error);
      // Still try to clear the stream reference
      this.currentStream = null;
      // And clear permissions in Redux
      try {
        store.dispatch(updateMediaPermissions({
          audio: false,
          video: false,
        }));
      } catch (reduxError) {
        console.error('❌ Failed to update Redux permissions during error recovery:', reduxError);
      }
    }
  }

  /**
   * Toggle a specific track type in a stream
   */
  toggleTrack(stream: MediaStream, trackType: 'audio' | 'video', enabled: boolean): boolean {
    if (!stream) {
      console.warn(`⚠️ No stream available to toggle ${trackType}`);
      return false;
    }

    const tracks = trackType === 'audio' ? stream.getAudioTracks() : stream.getVideoTracks();
    
    if (tracks.length === 0) {
      console.warn(`⚠️ No ${trackType} tracks found in stream`);
      return false;
    }

    tracks.forEach(track => {
      track.enabled = enabled;
      console.log(`${trackType === 'audio' ? '🎤' : '📹'} ${track.kind} track ${enabled ? 'enabled' : 'disabled'}`);
    });

    return true;
  }

  /**
   * Get media constraints for different call types
   * Following HTML test constraint patterns
   */
  getMediaConstraints(callType: 'audio' | 'video'): MediaConstraints {
    const baseConstraints: MediaConstraints = {
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
      video: false,
    };

    if (callType === 'video') {
      baseConstraints.video = {
        width: { ideal: 1280, max: 1920 },
        height: { ideal: 720, max: 1080 },
        frameRate: { ideal: 30, max: 60 },
        facingMode: 'user',
      };
    }

    return baseConstraints;
  }

  /**
   * Check if media devices are available
   */
  async checkMediaDevices(): Promise<{ hasAudio: boolean; hasVideo: boolean }> {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        return { hasAudio: false, hasVideo: false };
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      
      const hasAudio = devices.some(device => device.kind === 'audioinput');
      const hasVideo = devices.some(device => device.kind === 'videoinput');

      console.log(`🔍 Media devices available - Audio: ${hasAudio}, Video: ${hasVideo}`);
      
      return { hasAudio, hasVideo };
    } catch (error) {
      console.error('❌ Failed to enumerate media devices:', error);
      return { hasAudio: false, hasVideo: false };
    }
  }

  /**
   * Get current stream
   */
  getCurrentStream(): MediaStream | null {
    return this.currentStream;
  }

  /**
   * Set current stream
   */
  setCurrentStream(stream: MediaStream | null): void {
    this.currentStream = stream;
  }

  /**
   * Check if browser supports WebRTC
   */
  static isWebRTCSupported(): boolean {
    return !!(
      window.RTCPeerConnection &&
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia
    );
  }

  /**
   * Check if browser supports screen sharing
   */
  static isScreenShareSupported(): boolean {
    return !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getDisplayMedia
    );
  }

  /**
   * Get browser WebRTC capabilities
   */
  static getWebRTCCapabilities(): {
    supported: boolean;
    screenShare: boolean;
    audio: boolean;
    video: boolean;
  } {
    return {
      supported: MediaService.isWebRTCSupported(),
      screenShare: MediaService.isScreenShareSupported(),
      audio: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      video: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    };
  }
}
