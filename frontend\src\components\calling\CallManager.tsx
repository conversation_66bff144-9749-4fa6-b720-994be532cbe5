// frontend/src/components/calling/CallManager.tsx
import React, { useRef, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useSocket } from '../../contexts/SocketContext';
import { WebRTCService, MediaService, SignalingService } from '../../services/webrtc';
import {
  selectCallingState,
  selectCurrentCall,
  setLocalStream,
  setPeerConnection,
  updateCallStatus,
  setCallingError,
  resetCallState,
  startCallTimer,
  resetCleanupFlag,
} from '../../store/slices/callingSlice';
import { IncomingCallModal } from './IncomingCallModal';
import { ActiveCallInterface } from './ActiveCallInterface';
import type { AppDispatch } from '../../store';

export const CallManager: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { socket, sendWebRTCOffer, sendWebRTCAnswer } = useSocket();
  
  const callingState = useSelector(selectCallingState);
  const currentCall = useSelector(selectCurrentCall);
  
  // Service instances
  const webrtcServiceRef = useRef<WebRTCService | null>(null);
  const mediaServiceRef = useRef<MediaService | null>(null);
  const signalingServiceRef = useRef<SignalingService | null>(null);

  // Initialize services
  useEffect(() => {
    console.log('🔧 Initializing CallManager services');
    
    webrtcServiceRef.current = new WebRTCService();
    mediaServiceRef.current = new MediaService();
    
    if (socket) {
      signalingServiceRef.current = new SignalingService(socket, dispatch);
      webrtcServiceRef.current.setSignalingService(signalingServiceRef.current);
    }

    return () => {
      console.log('🔧 Cleaning up CallManager services');
      cleanup();
    };
  }, [socket, dispatch]);

  // Enhanced cleanup function with comprehensive media cleanup
  const cleanup = useCallback(() => {
    console.log('🧹 CallManager cleanup starting...');

    try {
      // Stop local stream with detailed logging
      const currentLocalStream = webrtcServiceRef.current?.getLocalStream();
      if (currentLocalStream) {
        console.log('🛑 Stopping local stream tracks...');
        currentLocalStream.getTracks().forEach((track, index) => {
          console.log(`🛑 Stopping ${track.kind} track ${index}: ${track.label}`);
          track.stop();
        });
        console.log('✅ Local stream tracks stopped');
      } else {
        console.log('ℹ️ No local stream to stop');
      }

      // Stop remote stream if it exists in Redux
      const currentRemoteStream = callingState.remoteStream;
      if (currentRemoteStream) {
        console.log('🛑 Stopping remote stream tracks...');
        currentRemoteStream.getTracks().forEach((track, index) => {
          console.log(`🛑 Stopping remote ${track.kind} track ${index}: ${track.label}`);
          track.stop();
        });
        console.log('✅ Remote stream tracks stopped');
      } else {
        console.log('ℹ️ No remote stream to stop');
      }

      // Close peer connection with detailed logging
      if (webrtcServiceRef.current) {
        console.log('🔒 Closing WebRTC peer connection...');
        webrtcServiceRef.current.closePeerConnection();
        console.log('✅ WebRTC peer connection closed');
      } else {
        console.log('ℹ️ No WebRTC service to cleanup');
      }

      // Reset Redux state
      console.log('🔄 Resetting call state in Redux...');
      dispatch(resetCallState());
      console.log('✅ Call state reset');

      console.log('✅ CallManager cleanup completed successfully');
    } catch (error) {
      console.error('❌ Error during CallManager cleanup:', error);
      // Still reset Redux state even if cleanup fails
      dispatch(resetCallState());
    }
  }, [dispatch, callingState.remoteStream]);

  // Handle call initiation
  const handleCallInitiation = useCallback(async (conversationId: string, callType: 'audio' | 'video') => {
    try {
      console.log('📞 Handling call initiation:', { conversationId, callType });
      
      if (!mediaServiceRef.current || !webrtcServiceRef.current) {
        throw new Error('Services not initialized');
      }

      // Get user media
      const constraints = {
        audio: true,
        video: callType === 'video'
      };

      const localStream = await mediaServiceRef.current.getUserMedia(constraints);
      dispatch(setLocalStream(localStream));

      // Initialize peer connection
      console.log('🔧 Initializing peer connection...');
      const peerConnection = await webrtcServiceRef.current.initializePeerConnection();
      console.log('✅ Peer connection initialized, dispatching to Redux');
      dispatch(setPeerConnection(peerConnection));

      // Add local stream to peer connection
      console.log('🎥 Adding local stream to peer connection...');
      await webrtcServiceRef.current.addLocalStream(localStream);
      console.log('✅ Local stream added successfully');

      console.log('✅ Call initiation setup complete');
    } catch (error) {
      console.error('❌ Call initiation failed:', error);
      dispatch(setCallingError(error instanceof Error ? error.message : 'Call initiation failed'));
    }
  }, [dispatch]);

  // Handle incoming call
  const handleIncomingCall = useCallback(async (callType: 'audio' | 'video') => {
    try {
      console.log('🔔 Handling incoming call:', callType);
      
      if (!mediaServiceRef.current || !webrtcServiceRef.current) {
        throw new Error('Services not initialized');
      }

      // Prepare media for incoming call
      const constraints = {
        audio: true,
        video: callType === 'video'
      };

      const localStream = await mediaServiceRef.current.getUserMedia(constraints);
      dispatch(setLocalStream(localStream));

      // Initialize peer connection
      const peerConnection = await webrtcServiceRef.current.initializePeerConnection();
      dispatch(setPeerConnection(peerConnection));

      // Add local stream to peer connection
      await webrtcServiceRef.current.addLocalStream(localStream);

      console.log('✅ Incoming call setup complete');
    } catch (error) {
      console.error('❌ Incoming call setup failed:', error);
      dispatch(setCallingError(error instanceof Error ? error.message : 'Failed to prepare for incoming call'));
    }
  }, [dispatch]);

  // Note: WebRTC handlers will be implemented when WebRTC signaling is integrated
  // These handlers will process WebRTC offers, answers, and ICE candidates received via socket events

  // Listen for call state changes
  useEffect(() => {
    if (currentCall.status === 'initiating' && currentCall.conversationId && currentCall.type) {
      handleCallInitiation(currentCall.conversationId, currentCall.type);
    } else if (currentCall.status === 'active' && currentCall.id && webrtcServiceRef.current) {
      // When call becomes active (answered), set the call ID and create WebRTC offer
      console.log('📞 Call is now active, creating WebRTC offer...');
      webrtcServiceRef.current.setCurrentCallId(currentCall.id);
      webrtcServiceRef.current.createOffer();
    }
  }, [currentCall.status, currentCall.conversationId, currentCall.type, currentCall.id, handleCallInitiation]);

  // Update WebRTC service with current call ID whenever it changes
  useEffect(() => {
    if (currentCall.id && webrtcServiceRef.current) {
      console.log(`🔗 Setting WebRTC service call ID: ${currentCall.id}`);
      webrtcServiceRef.current.setCurrentCallId(currentCall.id);
    }
  }, [currentCall.id]);

  // Listen for incoming calls
  useEffect(() => {
    if (callingState.incomingCallData && callingState.showIncomingCall) {
      handleIncomingCall(callingState.incomingCallData.callType);
    }
  }, [callingState.incomingCallData, callingState.showIncomingCall, handleIncomingCall]);

  // Set up WebRTC service callbacks and cross-references
  useEffect(() => {
    if (webrtcServiceRef.current && signalingServiceRef.current) {
      // Set up bidirectional service references
      webrtcServiceRef.current.setSignalingService(signalingServiceRef.current);
      signalingServiceRef.current.setWebRTCService(webrtcServiceRef.current);
      console.log('🔗 Services connected: WebRTC ↔ Signaling');
    }
  }, []);

  // Watch for cleanup trigger from socket events (call ended/declined)
  useEffect(() => {
    if (callingState.shouldCleanupMedia) {
      console.log('🧹 Cleanup triggered by socket event, stopping media streams...');
      cleanup();
      dispatch(resetCleanupFlag());
    }
  }, [callingState.shouldCleanupMedia, cleanup, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return (
    <>
      {/* Incoming call modal */}
      <IncomingCallModal />
      
      {/* Active call interface */}
      <ActiveCallInterface />
    </>
  );
};

export default CallManager;
